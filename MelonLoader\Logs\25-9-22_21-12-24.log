
[21:12:24.883] ------------------------------
[21:12:24.886] MelonLoader v0.7.0 Open-Beta
[21:12:24.887] OS: Windows 11
[21:12:24.887] Hash Code: 72EB65FC06D8CACD193CD21D9E7D708128C85D36C429D62C2BE9BB24217EF1EC
[21:12:24.887] ------------------------------
[21:12:24.887] Game Type: Il2cpp
[21:12:24.887] Game Arch: x64
[21:12:24.888] ------------------------------
[21:12:24.888] Command-Line: 
[21:12:24.888] ------------------------------
[21:12:24.888] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:12:24.888] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:12:24.888] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[21:12:24.889] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[21:12:24.889] Runtime Type: net6
[21:12:24.978] ------------------------------
[21:12:24.978] Game Name: BloonsTD6
[21:12:24.979] Game Developer: Ninja Kiwi
[21:12:24.981] Unity Version: 6000.0.58f1
[21:12:24.982] Game Version: UNKNOWN
[21:12:24.982] ------------------------------

[21:12:25.608] Preferences Loaded!

[21:12:25.620] Loading UserLibs...
[21:12:25.622] 0 UserLibs loaded.

[21:12:25.623] Loading Plugins...
[21:12:25.626] 0 Plugins loaded.

[21:12:26.000] Loading Il2CppAssemblyGenerator...
[21:12:26.037] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[21:12:26.384] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[21:12:26.384] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[21:12:26.385] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[21:12:26.385] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[21:12:26.388] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[21:12:26.389] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.4.6-ci.579+9d4599dc78d69ede49a2ee96a1ccf41eec02db5b
[21:12:26.389] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.58
[21:12:26.389] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[21:12:26.389] [Il2CppAssemblyGenerator] Downloading Cpp2IL...
[21:12:26.389] [Il2CppAssemblyGenerator] Downloading https://github.com/SamboyCoding/Cpp2IL/releases/download/2022.1.0-pre-release.19/Cpp2IL-2022.1.0-pre-release.19-Windows.exe to C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\Il2CppAssemblyGenerator\Cpp2IL\Cpp2IL.exe
[21:12:27.272] [Il2CppAssemblyGenerator] Processing Cpp2IL...
[21:12:27.275] [Il2CppAssemblyGenerator] Downloading Cpp2IL.Plugin.StrippedCodeRegSupport...
[21:12:27.276] [Il2CppAssemblyGenerator] Downloading https://github.com/SamboyCoding/Cpp2IL/releases/download/2022.1.0-pre-release.19/Cpp2IL.Plugin.StrippedCodeRegSupport.dll to C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\Il2CppAssemblyGenerator\Cpp2IL\Plugins\Cpp2IL.Plugin.StrippedCodeRegSupport.dll
[21:12:27.421] [Il2CppAssemblyGenerator] Processing Cpp2IL.Plugin.StrippedCodeRegSupport...
[21:12:27.422] [Il2CppAssemblyGenerator] Downloading UnityDependencies...
[21:12:27.422] [Il2CppAssemblyGenerator] Downloading https://github.com/LavaGang/MelonLoader.UnityDependencies/releases/download/6000.0.58/Managed.zip to C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\Il2CppAssemblyGenerator\UnityDependencies_6000.0.58.zip
[21:12:27.619] [Il2CppAssemblyGenerator] Processing UnityDependencies...
[21:12:27.619] [Il2CppAssemblyGenerator] Creating Directory C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\Il2CppAssemblyGenerator\UnityDependencies
[21:12:27.619] [Il2CppAssemblyGenerator] Extracting C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\Il2CppAssemblyGenerator\UnityDependencies_6000.0.58.zip to C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\Il2CppAssemblyGenerator\UnityDependencies
[21:12:27.670] [Il2CppAssemblyGenerator] Checking GameAssembly...
[21:12:27.808] [Il2CppAssemblyGenerator] Assembly Generation Needed!
[21:12:27.830] [Il2CppAssemblyGenerator] Executing Cpp2IL...
[21:12:27.831] [Il2CppAssemblyGenerator] "C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\Il2CppAssemblyGenerator\Cpp2IL\Cpp2IL.exe" --game-path "C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6" --exe-name "BloonsTD6" --output-as dummydll --use-processor attributeanalyzer attributeinjector
[21:12:27.944] [Il2CppAssemblyGenerator] ===Cpp2IL by Samboy063===
[21:12:27.944] [Il2CppAssemblyGenerator] A Tool to Reverse Unity's "il2cpp" Build Process.
[21:12:27.950] [Il2CppAssemblyGenerator] Version 2022.1.0-pre-release.19+edbb9949b3f999a44bb42aea14f357d6e0e7820f
[21:12:27.950] [Il2CppAssemblyGenerator] 
[21:12:27.959] [Il2CppAssemblyGenerator] [Warn] [Program] NO_COLOR set, disabling ANSI color codes as you requested.
[21:12:27.968] [Il2CppAssemblyGenerator] [Info] [Program] Running on Win32NT
[21:12:28.067] [Il2CppAssemblyGenerator] [Info] [Plugins] Loading plugins from C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\Il2CppAssemblyGenerator\Cpp2IL\Plugins...
[21:12:28.150] [Il2CppAssemblyGenerator] [Info] [Plugins] Using Plugin: Cpp2IL Built-In
[21:12:28.150] [Il2CppAssemblyGenerator] [Info] [Plugins] Using Plugin: Stripped CodeReg Support
[21:12:28.204] [Il2CppAssemblyGenerator] [Info] [Program] Determined game's unity version to be 6000.0.58a0
[21:12:28.250] [Il2CppAssemblyGenerator] [Info] [Library] Initializing Metadata...
[21:12:28.253] [Il2CppAssemblyGenerator] [Info] [Library] 	Using actual IL2CPP Metadata version 31
[21:12:28.636] [Il2CppAssemblyGenerator] [Info] [Library] Initialized Metadata in 384ms
[21:12:28.636] [Il2CppAssemblyGenerator] [Info] [Library] Using binary type Portable Executable (from LibCpp2IL)
[21:12:28.637] [Il2CppAssemblyGenerator] [Info] [Library] Searching Binary for Required Data...
[21:12:29.095] [Il2CppAssemblyGenerator] [Info] [Library] Got Binary codereg: 0x1836C7F50, metareg: 0x183EF3ED0 in 459ms.
[21:12:29.096] [Il2CppAssemblyGenerator] [Info] [Library] Initializing Binary...
[21:12:29.296] [Il2CppAssemblyGenerator] [Info] [Library] Initialized Binary in 200ms
[21:12:29.484] [Il2CppAssemblyGenerator] [Info] [Library] Mapping pointers to Il2CppMethodDefinitions...Processed 150117 OK (188ms)
[21:12:29.498] [Il2CppAssemblyGenerator] [Info] [Program] Creating application model...
[21:12:31.609] [Il2CppAssemblyGenerator] [Info] [Program] Application model created in 2111.4982ms
[21:12:31.609] [Il2CppAssemblyGenerator] [Info] [Program] Pre-processing processing layers...
[21:12:31.610] [Il2CppAssemblyGenerator] [Info] [Program]     CustomAttribute Analyzer...
[21:12:31.610] [Il2CppAssemblyGenerator] [Info] [Program]     CustomAttribute Analyzer finished in 0.084ms
[21:12:31.610] [Il2CppAssemblyGenerator] [Info] [Program]     Attribute Injector...
[21:12:31.610] [Il2CppAssemblyGenerator] [Info] [Program]     Attribute Injector finished in 0.0045ms
[21:12:31.610] [Il2CppAssemblyGenerator] [Info] [Program] Invoking processing layers...
[21:12:31.610] [Il2CppAssemblyGenerator] [Info] [Program]     CustomAttribute Analyzer...
[21:12:31.710] [Il2CppAssemblyGenerator] [Info] [Program]     CustomAttribute Analyzer finished in 100.2115ms
[21:12:31.711] [Il2CppAssemblyGenerator] [Info] [Program]     Attribute Injector...
[21:12:32.199] [Il2CppAssemblyGenerator] [Info] [Program]     Attribute Injector finished in 488.7797ms
[21:12:32.200] [Il2CppAssemblyGenerator] [Info] [Program] Outputting as DLL output format for backwards compatibility. to C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\MelonLoader\Dependencies\Il2CppAssemblyGenerator\Cpp2IL\cpp2il_out...
[21:12:37.288] [Il2CppAssemblyGenerator] [Info] [Program] Finished outputting in 5088.8717ms
[21:12:37.288] [Il2CppAssemblyGenerator] [Info] [Program] Done. Total execution time: 9081.0272ms
[21:12:37.475] [Il2CppAssemblyGenerator] Reading dumped assemblies for interop generation...
[21:12:37.622] [Il2CppAssemblyGenerator] Generating Interop Assemblies...
[21:12:37.629] [Il2CppAssemblyGenerator] Reading assemblies...
[21:12:37.672] [Il2CppAssemblyGenerator] Done in 00:00:00.0423432
[21:12:37.672] [Il2CppAssemblyGenerator] Reading unity assemblies...
[21:12:37.699] [Il2CppAssemblyGenerator] Done in 00:00:00.0260428
[21:12:37.699] [Il2CppAssemblyGenerator] Creating rewrite assemblies...
[21:12:37.712] [Il2CppAssemblyGenerator] Done in 00:00:00.0121618
[21:12:37.713] [Il2CppAssemblyGenerator] Computing renames...
[21:12:37.833] [Il2CppAssemblyGenerator] Done in 00:00:00.1198772
[21:12:37.833] [Il2CppAssemblyGenerator] Creating typedefs...
[21:12:37.929] [Il2CppAssemblyGenerator] Done in 00:00:00.0953921
[21:12:37.930] [Il2CppAssemblyGenerator] Computing struct blittability...
[21:12:38.020] [Il2CppAssemblyGenerator] Done in 00:00:00.0900218
[21:12:38.020] [Il2CppAssemblyGenerator] Filling typedefs...
[21:12:38.193] [Il2CppAssemblyGenerator] Done in 00:00:00.1719988
[21:12:38.193] [Il2CppAssemblyGenerator] Filling generic constraints...
[21:12:38.222] [Il2CppAssemblyGenerator] Done in 00:00:00.0284045
[21:12:38.222] [Il2CppAssemblyGenerator] Creating members...
[21:12:40.558] [Il2CppAssemblyGenerator] Done in 00:00:02.3353310
[21:12:40.558] [Il2CppAssemblyGenerator] Scanning method cross-references...
[21:12:41.827] [Il2CppAssemblyGenerator] Done in 00:00:01.2679299
[21:12:41.827] [Il2CppAssemblyGenerator] Finalizing method declarations...
[21:12:45.288] [Il2CppAssemblyGenerator] Done in 00:00:03.4596480
[21:12:45.288] [Il2CppAssemblyGenerator] 109 total potentially dead methods
[21:12:45.288] [Il2CppAssemblyGenerator] Filling method parameters...
[21:12:46.709] [Il2CppAssemblyGenerator] Done in 00:00:01.4208874
[21:12:46.710] [Il2CppAssemblyGenerator] Creating static constructors...
[21:12:47.614] [Il2CppAssemblyGenerator] Done in 00:00:00.9041107
[21:12:47.615] [Il2CppAssemblyGenerator] Creating value type fields...
[21:12:47.700] [Il2CppAssemblyGenerator] Done in 00:00:00.0847176
[21:12:47.700] [Il2CppAssemblyGenerator] Creating enums...
[21:12:47.817] [Il2CppAssemblyGenerator] Done in 00:00:00.1168009
[21:12:47.818] [Il2CppAssemblyGenerator] Creating IntPtr constructors...
[21:12:47.946] [Il2CppAssemblyGenerator] Done in 00:00:00.1279233
[21:12:47.946] [Il2CppAssemblyGenerator] Creating non-blittable struct constructors...
[21:12:47.952] [Il2CppAssemblyGenerator] Done in 00:00:00.0052857
[21:12:47.952] [Il2CppAssemblyGenerator] Creating generic method static constructors...
[21:12:48.065] [Il2CppAssemblyGenerator] Done in 00:00:00.1125642
[21:12:48.066] [Il2CppAssemblyGenerator] Creating field accessors...
[21:12:50.834] [Il2CppAssemblyGenerator] Done in 00:00:02.7677225
[21:12:50.835] [Il2CppAssemblyGenerator] Filling methods...
[21:12:53.432] [Il2CppAssemblyGenerator] Done in 00:00:02.5968288
[21:12:53.433] [Il2CppAssemblyGenerator] Generating implicit conversions...
[21:12:53.485] [Il2CppAssemblyGenerator] Done in 00:00:00.0519324
[21:12:53.485] [Il2CppAssemblyGenerator] Creating properties...
[21:12:53.855] [Il2CppAssemblyGenerator] Done in 00:00:00.3687179
[21:12:53.855] [Il2CppAssemblyGenerator] Unstripping types...
[21:12:53.939] [Il2CppAssemblyGenerator] Done in 00:00:00.0835955
[21:12:53.939] [Il2CppAssemblyGenerator] Unstripping fields...
[21:12:53.950] [Il2CppAssemblyGenerator] Restored 524 fields
[21:12:53.950] [Il2CppAssemblyGenerator] Failed to restore 13 fields
[21:12:53.950] [Il2CppAssemblyGenerator] Done in 00:00:00.0103976
[21:12:53.950] [Il2CppAssemblyGenerator] Unstripping methods...
[21:12:54.676] [Il2CppAssemblyGenerator] Restored 16681 methods
[21:12:54.676] [Il2CppAssemblyGenerator] Failed to restore 1174 methods
[21:12:54.676] [Il2CppAssemblyGenerator] Done in 00:00:00.7260525
[21:12:54.677] [Il2CppAssemblyGenerator] Unstripping method bodies...
[21:12:54.969] [Il2CppAssemblyGenerator] IL unstrip statistics: 10198 successful, 1461 failed
[21:12:54.970] [Il2CppAssemblyGenerator] Done in 00:00:00.2931808
[21:12:54.970] [Il2CppAssemblyGenerator] Writing xref cache...
[21:12:55.718] [Il2CppAssemblyGenerator] Done in 00:00:00.7473195
[21:12:55.718] [Il2CppAssemblyGenerator] Writing assemblies...
[21:13:10.289] [Il2CppAssemblyGenerator] Done in 00:00:14.5702196
[21:13:10.289] [Il2CppAssemblyGenerator] Writing method pointer map...
[21:13:10.337] [Il2CppAssemblyGenerator] Done in 00:00:00.0468518
[21:13:10.337] [Il2CppAssemblyGenerator] Done!
[21:13:10.338] [Il2CppAssemblyGenerator] Cleaning up...
[21:13:10.338] [Il2CppAssemblyGenerator] Interop Generation Complete!
[21:13:10.339] [Il2CppAssemblyGenerator] Moving Assembly-CSharp-firstpass.dll
[21:13:10.340] [Il2CppAssemblyGenerator] Moving Assembly-CSharp.dll
[21:13:10.341] [Il2CppAssemblyGenerator] Moving Il2CppDebugLog.dll
[21:13:10.342] [Il2CppAssemblyGenerator] Moving Il2CppFacepunch.Steamworks.dll
[21:13:10.342] [Il2CppAssemblyGenerator] Moving Il2CppMono.Security.dll
[21:13:10.342] [Il2CppAssemblyGenerator] Moving Il2Cppmscorlib.dll
[21:13:10.343] [Il2CppAssemblyGenerator] Moving Il2CppNativeShare.Runtime.dll
[21:13:10.343] [Il2CppAssemblyGenerator] Moving Il2CppNCalc.dll
[21:13:10.343] [Il2CppAssemblyGenerator] Moving Il2CppNewtonsoft.Json.dll
[21:13:10.343] [Il2CppAssemblyGenerator] Moving Il2CppNewtonsoft.Json.UnityConverters.dll
[21:13:10.344] [Il2CppAssemblyGenerator] Moving Il2CppNinjaKiwi.Common.Attribution.dll
[21:13:10.344] [Il2CppAssemblyGenerator] Moving Il2CppNinjaKiwi.Common.dll
[21:13:10.344] [Il2CppAssemblyGenerator] Moving Il2CppNinjaKiwi.GUTS.dll
[21:13:10.345] [Il2CppAssemblyGenerator] Moving Il2CppNinjaKiwi.LiNK.Client.dll
[21:13:10.345] [Il2CppAssemblyGenerator] Moving Il2CppNinjaKiwi.LiNK.dll
[21:13:10.346] [Il2CppAssemblyGenerator] Moving Il2CppPurchasing.Common.dll
[21:13:10.346] [Il2CppAssemblyGenerator] Moving Il2CppRegEdit.dll
[21:13:10.347] [Il2CppAssemblyGenerator] Moving Il2CppSteam.dll
[21:13:10.347] [Il2CppAssemblyGenerator] Moving Il2CppSystem.Configuration.dll
[21:13:10.347] [Il2CppAssemblyGenerator] Moving Il2CppSystem.Core.dll
[21:13:10.348] [Il2CppAssemblyGenerator] Moving Il2CppSystem.Data.dll
[21:13:10.348] [Il2CppAssemblyGenerator] Moving Il2CppSystem.dll
[21:13:10.348] [Il2CppAssemblyGenerator] Moving Il2CppSystem.Drawing.dll
[21:13:10.349] [Il2CppAssemblyGenerator] Moving Il2CppSystem.Net.Http.dll
[21:13:10.349] [Il2CppAssemblyGenerator] Moving Il2CppSystem.Numerics.dll
[21:13:10.349] [Il2CppAssemblyGenerator] Moving Il2CppSystem.Runtime.Serialization.dll
[21:13:10.349] [Il2CppAssemblyGenerator] Moving Il2CppSystem.Xml.dll
[21:13:10.350] [Il2CppAssemblyGenerator] Moving Il2CppSystem.Xml.Linq.dll
[21:13:10.350] [Il2CppAssemblyGenerator] Moving Il2CppTwitch.dll
[21:13:10.351] [Il2CppAssemblyGenerator] Moving Il2CppUniWebView-CSharp.dll
[21:13:10.351] [Il2CppAssemblyGenerator] Moving Il2Cpp__Generated.dll
[21:13:10.351] [Il2CppAssemblyGenerator] Moving MethodAddressToToken.db
[21:13:10.351] [Il2CppAssemblyGenerator] Moving MethodXrefScanCache.db
[21:13:10.352] [Il2CppAssemblyGenerator] Moving Unity.2D.Common.Runtime.dll
[21:13:10.352] [Il2CppAssemblyGenerator] Moving Unity.2D.SpriteShape.Runtime.dll
[21:13:10.352] [Il2CppAssemblyGenerator] Moving Unity.Addressables.dll
[21:13:10.352] [Il2CppAssemblyGenerator] Moving Unity.Burst.dll
[21:13:10.353] [Il2CppAssemblyGenerator] Moving Unity.Burst.Unsafe.dll
[21:13:10.353] [Il2CppAssemblyGenerator] Moving Unity.Collections.dll
[21:13:10.353] [Il2CppAssemblyGenerator] Moving Unity.InputSystem.dll
[21:13:10.354] [Il2CppAssemblyGenerator] Moving Unity.InputSystem.ForUI.dll
[21:13:10.354] [Il2CppAssemblyGenerator] Moving Unity.Mathematics.dll
[21:13:10.354] [Il2CppAssemblyGenerator] Moving Unity.RenderPipeline.Universal.ShaderLibrary.dll
[21:13:10.355] [Il2CppAssemblyGenerator] Moving Unity.RenderPipelines.Core.Runtime.dll
[21:13:10.355] [Il2CppAssemblyGenerator] Moving Unity.RenderPipelines.Core.Runtime.Shared.dll
[21:13:10.355] [Il2CppAssemblyGenerator] Moving Unity.RenderPipelines.GPUDriven.Runtime.dll
[21:13:10.356] [Il2CppAssemblyGenerator] Moving Unity.RenderPipelines.Universal.Runtime.dll
[21:13:10.357] [Il2CppAssemblyGenerator] Moving Unity.ResourceManager.dll
[21:13:10.357] [Il2CppAssemblyGenerator] Moving Unity.Services.Core.Configuration.dll
[21:13:10.357] [Il2CppAssemblyGenerator] Moving Unity.Services.Core.Device.dll
[21:13:10.357] [Il2CppAssemblyGenerator] Moving Unity.Services.Core.dll
[21:13:10.358] [Il2CppAssemblyGenerator] Moving Unity.Services.Core.Environments.dll
[21:13:10.358] [Il2CppAssemblyGenerator] Moving Unity.Services.Core.Environments.Internal.dll
[21:13:10.358] [Il2CppAssemblyGenerator] Moving Unity.Services.Core.Internal.dll
[21:13:10.358] [Il2CppAssemblyGenerator] Moving Unity.Services.Core.Registration.dll
[21:13:10.359] [Il2CppAssemblyGenerator] Moving Unity.Services.Core.Scheduler.dll
[21:13:10.359] [Il2CppAssemblyGenerator] Moving Unity.Services.Core.Telemetry.dll
[21:13:10.360] [Il2CppAssemblyGenerator] Moving Unity.Services.Core.Threading.dll
[21:13:10.361] [Il2CppAssemblyGenerator] Moving Unity.TextMeshPro.dll
[21:13:10.361] [Il2CppAssemblyGenerator] Moving UnityEngine.AccessibilityModule.dll
[21:13:10.362] [Il2CppAssemblyGenerator] Moving UnityEngine.AIModule.dll
[21:13:10.362] [Il2CppAssemblyGenerator] Moving UnityEngine.AndroidJNIModule.dll
[21:13:10.362] [Il2CppAssemblyGenerator] Moving UnityEngine.AnimationModule.dll
[21:13:10.363] [Il2CppAssemblyGenerator] Moving UnityEngine.ARModule.dll
[21:13:10.363] [Il2CppAssemblyGenerator] Moving UnityEngine.AssetBundleModule.dll
[21:13:10.363] [Il2CppAssemblyGenerator] Moving UnityEngine.AudioModule.dll
[21:13:10.364] [Il2CppAssemblyGenerator] Moving UnityEngine.ClothModule.dll
[21:13:10.364] [Il2CppAssemblyGenerator] Moving UnityEngine.ContentLoadModule.dll
[21:13:10.365] [Il2CppAssemblyGenerator] Moving UnityEngine.CoreModule.dll
[21:13:10.365] [Il2CppAssemblyGenerator] Moving UnityEngine.CrashReportingModule.dll
[21:13:10.365] [Il2CppAssemblyGenerator] Moving UnityEngine.DirectorModule.dll
[21:13:10.365] [Il2CppAssemblyGenerator] Moving UnityEngine.dll
[21:13:10.366] [Il2CppAssemblyGenerator] Moving UnityEngine.DSPGraphModule.dll
[21:13:10.366] [Il2CppAssemblyGenerator] Moving UnityEngine.GameCenterModule.dll
[21:13:10.366] [Il2CppAssemblyGenerator] Moving UnityEngine.GIModule.dll
[21:13:10.367] [Il2CppAssemblyGenerator] Moving UnityEngine.GraphicsStateCollectionSerializerModule.dll
[21:13:10.367] [Il2CppAssemblyGenerator] Moving UnityEngine.GridModule.dll
[21:13:10.368] [Il2CppAssemblyGenerator] Moving UnityEngine.HierarchyCoreModule.dll
[21:13:10.368] [Il2CppAssemblyGenerator] Moving UnityEngine.HotReloadModule.dll
[21:13:10.368] [Il2CppAssemblyGenerator] Moving UnityEngine.ImageConversionModule.dll
[21:13:10.369] [Il2CppAssemblyGenerator] Moving UnityEngine.IMGUIModule.dll
[21:13:10.369] [Il2CppAssemblyGenerator] Moving UnityEngine.InputForUIModule.dll
[21:13:10.369] [Il2CppAssemblyGenerator] Moving UnityEngine.InputLegacyModule.dll
[21:13:10.369] [Il2CppAssemblyGenerator] Moving UnityEngine.InputModule.dll
[21:13:10.370] [Il2CppAssemblyGenerator] Moving UnityEngine.JSONSerializeModule.dll
[21:13:10.370] [Il2CppAssemblyGenerator] Moving UnityEngine.LocalizationModule.dll
[21:13:10.370] [Il2CppAssemblyGenerator] Moving UnityEngine.MarshallingModule.dll
[21:13:10.371] [Il2CppAssemblyGenerator] Moving UnityEngine.MultiplayerModule.dll
[21:13:10.371] [Il2CppAssemblyGenerator] Moving UnityEngine.ParticleSystemModule.dll
[21:13:10.371] [Il2CppAssemblyGenerator] Moving UnityEngine.PerformanceReportingModule.dll
[21:13:10.371] [Il2CppAssemblyGenerator] Moving UnityEngine.Physics2DModule.dll
[21:13:10.372] [Il2CppAssemblyGenerator] Moving UnityEngine.PhysicsModule.dll
[21:13:10.372] [Il2CppAssemblyGenerator] Moving UnityEngine.PropertiesModule.dll
[21:13:10.372] [Il2CppAssemblyGenerator] Moving UnityEngine.Purchasing.AppleCore.dll
[21:13:10.373] [Il2CppAssemblyGenerator] Moving UnityEngine.Purchasing.AppleMacosStub.dll
[21:13:10.376] [Il2CppAssemblyGenerator] Moving UnityEngine.Purchasing.AppleStub.dll
[21:13:10.377] [Il2CppAssemblyGenerator] Moving UnityEngine.Purchasing.dll
[21:13:10.378] [Il2CppAssemblyGenerator] Moving UnityEngine.Purchasing.SecurityCore.dll
[21:13:10.379] [Il2CppAssemblyGenerator] Moving UnityEngine.Purchasing.SecurityStub.dll
[21:13:10.379] [Il2CppAssemblyGenerator] Moving UnityEngine.Purchasing.Stores.dll
[21:13:10.379] [Il2CppAssemblyGenerator] Moving UnityEngine.Purchasing.WinRTCore.dll
[21:13:10.380] [Il2CppAssemblyGenerator] Moving UnityEngine.Purchasing.WinRTStub.dll
[21:13:10.380] [Il2CppAssemblyGenerator] Moving UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
[21:13:10.380] [Il2CppAssemblyGenerator] Moving UnityEngine.ScreenCaptureModule.dll
[21:13:10.381] [Il2CppAssemblyGenerator] Moving UnityEngine.ShaderVariantAnalyticsModule.dll
[21:13:10.381] [Il2CppAssemblyGenerator] Moving UnityEngine.SharedInternalsModule.dll
[21:13:10.382] [Il2CppAssemblyGenerator] Moving UnityEngine.SpriteMaskModule.dll
[21:13:10.382] [Il2CppAssemblyGenerator] Moving UnityEngine.SpriteShapeModule.dll
[21:13:10.382] [Il2CppAssemblyGenerator] Moving UnityEngine.StreamingModule.dll
[21:13:10.383] [Il2CppAssemblyGenerator] Moving UnityEngine.SubstanceModule.dll
[21:13:10.383] [Il2CppAssemblyGenerator] Moving UnityEngine.SubsystemsModule.dll
[21:13:10.383] [Il2CppAssemblyGenerator] Moving UnityEngine.TerrainModule.dll
[21:13:10.384] [Il2CppAssemblyGenerator] Moving UnityEngine.TerrainPhysicsModule.dll
[21:13:10.384] [Il2CppAssemblyGenerator] Moving UnityEngine.TextCoreFontEngineModule.dll
[21:13:10.384] [Il2CppAssemblyGenerator] Moving UnityEngine.TextCoreTextEngineModule.dll
[21:13:10.385] [Il2CppAssemblyGenerator] Moving UnityEngine.TextRenderingModule.dll
[21:13:10.385] [Il2CppAssemblyGenerator] Moving UnityEngine.TilemapModule.dll
[21:13:10.385] [Il2CppAssemblyGenerator] Moving UnityEngine.TLSModule.dll
[21:13:10.385] [Il2CppAssemblyGenerator] Moving UnityEngine.UI.dll
[21:13:10.386] [Il2CppAssemblyGenerator] Moving UnityEngine.UIElementsModule.dll
[21:13:10.386] [Il2CppAssemblyGenerator] Moving UnityEngine.UIModule.dll
[21:13:10.386] [Il2CppAssemblyGenerator] Moving UnityEngine.UmbraModule.dll
[21:13:10.387] [Il2CppAssemblyGenerator] Moving UnityEngine.UnityAnalyticsCommonModule.dll
[21:13:10.387] [Il2CppAssemblyGenerator] Moving UnityEngine.UnityAnalyticsModule.dll
[21:13:10.387] [Il2CppAssemblyGenerator] Moving UnityEngine.UnityConnectModule.dll
[21:13:10.407] [Il2CppAssemblyGenerator] Moving UnityEngine.UnityCurlModule.dll
[21:13:10.407] [Il2CppAssemblyGenerator] Moving UnityEngine.UnityTestProtocolModule.dll
[21:13:10.408] [Il2CppAssemblyGenerator] Moving UnityEngine.UnityWebRequestAssetBundleModule.dll
[21:13:10.408] [Il2CppAssemblyGenerator] Moving UnityEngine.UnityWebRequestAudioModule.dll
[21:13:10.408] [Il2CppAssemblyGenerator] Moving UnityEngine.UnityWebRequestModule.dll
[21:13:10.409] [Il2CppAssemblyGenerator] Moving UnityEngine.UnityWebRequestTextureModule.dll
[21:13:10.427] [Il2CppAssemblyGenerator] Moving UnityEngine.UnityWebRequestWWWModule.dll
[21:13:10.428] [Il2CppAssemblyGenerator] Moving UnityEngine.VehiclesModule.dll
[21:13:10.429] [Il2CppAssemblyGenerator] Moving UnityEngine.VFXModule.dll
[21:13:10.429] [Il2CppAssemblyGenerator] Moving UnityEngine.VideoModule.dll
[21:13:10.429] [Il2CppAssemblyGenerator] Moving UnityEngine.VRModule.dll
[21:13:10.430] [Il2CppAssemblyGenerator] Moving UnityEngine.WindModule.dll
[21:13:10.441] [Il2CppAssemblyGenerator] Moving UnityEngine.XRModule.dll
[21:13:10.443] [Il2CppAssemblyGenerator] Assembly Generation Successful!

[21:13:10.444] Loading Mods...
[21:13:10.457] ------------------------------
[21:13:10.477] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[21:13:10.477] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[21:13:10.482] Melon Assembly loaded: '.\Mods\Unlimited5thTiers.dll'
[21:13:10.482] SHA256 Hash: '7DD1509DD4F03946BCBD551304A554DFFB37BA70B9587E2FA587C3A189BCC351'

[21:13:11.334] ------------------------------
[21:13:11.334] BloonsTD6 Mod Helper v3.4.12
[21:13:11.334] by Gurrenm4 and Doombubbles
[21:13:11.334] Assembly: Btd6ModHelper.dll
[21:13:11.334] ------------------------------
[21:13:11.337] ------------------------------
[21:13:11.337] Unlimited 5th Tiers + v1.1.9
[21:13:11.338] by doombubbles
[21:13:11.338] Assembly: Unlimited5thTiers.dll
[21:13:11.338] ------------------------------
[21:13:11.338] ------------------------------
[21:13:11.338] 2 Mods loaded.

[21:13:12.942] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[21:13:13.046] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
