
[21:13:25.937] ------------------------------
[21:13:25.940] MelonLoader v0.7.0 Open-Beta
[21:13:25.940] OS: Windows 11
[21:13:25.941] Hash Code: 72EB65FC06D8CACD193CD21D9E7D708128C85D36C429D62C2BE9BB24217EF1EC
[21:13:25.941] ------------------------------
[21:13:25.941] Game Type: Il2cpp
[21:13:25.941] Game Arch: x64
[21:13:25.941] ------------------------------
[21:13:25.942] Command-Line: 
[21:13:25.942] ------------------------------
[21:13:25.942] Core::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:13:25.942] Game::BasePath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6
[21:13:25.942] Game::DataPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6_Data
[21:13:25.942] Game::ApplicationPath = C:\Program Files (x86)\Steam\steamapps\common\BloonsTD6\BloonsTD6.exe
[21:13:25.942] Runtime Type: net6
[21:13:26.008] ------------------------------
[21:13:26.009] Game Name: BloonsTD6
[21:13:26.009] Game Developer: Ninja Kiwi
[21:13:26.010] Unity Version: 6000.0.58f1
[21:13:26.010] Game Version: UNKNOWN
[21:13:26.010] ------------------------------

[21:13:26.603] Preferences Loaded!

[21:13:26.614] Loading UserLibs...
[21:13:26.616] 0 UserLibs loaded.

[21:13:26.616] Loading Plugins...
[21:13:26.620] 0 Plugins loaded.

[21:13:26.991] Loading Il2CppAssemblyGenerator...
[21:13:27.036] [Il2CppAssemblyGenerator] Contacting RemoteAPI...
[21:13:27.337] [Il2CppAssemblyGenerator] RemoteAPI.DumperVersion = null
[21:13:27.337] [Il2CppAssemblyGenerator] RemoteAPI.ObfuscationRegex = null
[21:13:27.338] [Il2CppAssemblyGenerator] RemoteAPI.MappingURL = null
[21:13:27.338] [Il2CppAssemblyGenerator] RemoteAPI.MappingFileSHA512 = null
[21:13:27.341] [Il2CppAssemblyGenerator] Using Cpp2IL Version: 2022.1.0-pre-release.19
[21:13:27.341] [Il2CppAssemblyGenerator] Using Il2CppInterop Version = 1.4.6-ci.579+9d4599dc78d69ede49a2ee96a1ccf41eec02db5b
[21:13:27.341] [Il2CppAssemblyGenerator] Using Unity Dependencies Version = 6000.0.58
[21:13:27.341] [Il2CppAssemblyGenerator] Using Deobfuscation Regex = null
[21:13:27.342] [Il2CppAssemblyGenerator] Cpp2IL is up to date.
[21:13:27.342] [Il2CppAssemblyGenerator] Cpp2IL.Plugin.StrippedCodeRegSupport is up to date.
[21:13:27.342] [Il2CppAssemblyGenerator] UnityDependencies is up to date.
[21:13:27.342] [Il2CppAssemblyGenerator] Checking GameAssembly...
[21:13:27.481] [Il2CppAssemblyGenerator] Assembly is up to date. No Generation Needed.

[21:13:27.482] Loading Mods...
[21:13:27.511] ------------------------------
[21:13:27.544] Melon Assembly loaded: '.\Mods\Btd6ModHelper.dll'
[21:13:27.545] SHA256 Hash: '25899E3FA4312B2A8B0D71E104A5860C8295341B6EAD7A32C32BED95E9DB87EC'
[21:13:27.549] Melon Assembly loaded: '.\Mods\Unlimited5thTiers.dll'
[21:13:27.549] SHA256 Hash: '7DD1509DD4F03946BCBD551304A554DFFB37BA70B9587E2FA587C3A189BCC351'

[21:13:28.089] ------------------------------
[21:13:28.090] BloonsTD6 Mod Helper v3.4.12
[21:13:28.090] by Gurrenm4 and Doombubbles
[21:13:28.090] Assembly: Btd6ModHelper.dll
[21:13:28.090] ------------------------------
[21:13:28.094] ------------------------------
[21:13:28.094] Unlimited 5th Tiers + v1.1.9
[21:13:28.094] by doombubbles
[21:13:28.095] Assembly: Unlimited5thTiers.dll
[21:13:28.095] ------------------------------
[21:13:28.096] ------------------------------
[21:13:28.096] 2 Mods loaded.

[21:13:29.474] [Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[21:13:29.581] [Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
