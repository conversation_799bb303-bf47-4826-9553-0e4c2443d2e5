[Il2CppAssemblyGenerator]
GameAssemblyHash = "1870A6C23A6E5038FEF734B871767D1F419686D51BA4ADE62E2D219BCB927E1F83F183EA6B18C91F919256C2D721F6C7022B184C202297F728B8AED61BCC395B"
UnityVersion = "6000.0.58"
DumperVersion = "2022.1.0-pre-release.19"
DumperSCRSVersion = "2022.1.0-pre-release.19"
UseInterop = true
OldFiles = [ "Assembly-CSharp-firstpass.dll", "Assembly-CSharp.dll", "Il2CppDebugLog.dll", "Il2CppFacepunch.Steamworks.dll", "Il2CppMono.Security.dll", "Il2Cppmscorlib.dll", "Il2CppNativeShare.Runtime.dll", "Il2CppNCalc.dll", "Il2CppNewtonsoft.Json.dll", "Il2CppNewtonsoft.Json.UnityConverters.dll", "Il2CppNinjaKiwi.Common.Attribution.dll", "Il2CppNinjaKiwi.Common.dll", "Il2CppNinjaKiwi.GUTS.dll", "Il2CppNinjaKiwi.LiNK.Client.dll", "Il2CppNinjaKiwi.LiNK.dll", "Il2CppPurchasing.Common.dll", "Il2CppRegEdit.dll", "Il2CppSteam.dll", "Il2CppSystem.Configuration.dll", "Il2CppSystem.Core.dll", "Il2CppSystem.Data.dll", "Il2CppSystem.dll", "Il2CppSystem.Drawing.dll", "Il2CppSystem.Net.Http.dll", "Il2CppSystem.Numerics.dll", "Il2CppSystem.Runtime.Serialization.dll", "Il2CppSystem.Xml.dll", "Il2CppSystem.Xml.Linq.dll", "Il2CppTwitch.dll", "Il2CppUniWebView-CSharp.dll", "Il2Cpp__Generated.dll", "MethodAddressToToken.db", "MethodXrefScanCache.db", "Unity.2D.Common.Runtime.dll", "Unity.2D.SpriteShape.Runtime.dll", "Unity.Addressables.dll", "Unity.Burst.dll", "Unity.Burst.Unsafe.dll", "Unity.Collections.dll", "Unity.InputSystem.dll", "Unity.InputSystem.ForUI.dll", "Unity.Mathematics.dll", "Unity.RenderPipeline.Universal.ShaderLibrary.dll", "Unity.RenderPipelines.Core.Runtime.dll", "Unity.RenderPipelines.Core.Runtime.Shared.dll", "Unity.RenderPipelines.GPUDriven.Runtime.dll", "Unity.RenderPipelines.Universal.Runtime.dll", "Unity.ResourceManager.dll", "Unity.Services.Core.Configuration.dll", "Unity.Services.Core.Device.dll", "Unity.Services.Core.dll", "Unity.Services.Core.Environments.dll", "Unity.Services.Core.Environments.Internal.dll", "Unity.Services.Core.Internal.dll", "Unity.Services.Core.Registration.dll", "Unity.Services.Core.Scheduler.dll", "Unity.Services.Core.Telemetry.dll", "Unity.Services.Core.Threading.dll", "Unity.TextMeshPro.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AIModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ContentLoadModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GraphicsStateCollectionSerializerModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HierarchyCoreModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.InputForUIModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.MarshallingModule.dll", "UnityEngine.MultiplayerModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.PropertiesModule.dll", "UnityEngine.Purchasing.AppleCore.dll", "UnityEngine.Purchasing.AppleMacosStub.dll", "UnityEngine.Purchasing.AppleStub.dll", "UnityEngine.Purchasing.dll", "UnityEngine.Purchasing.SecurityCore.dll", "UnityEngine.Purchasing.SecurityStub.dll", "UnityEngine.Purchasing.Stores.dll", "UnityEngine.Purchasing.WinRTCore.dll", "UnityEngine.Purchasing.WinRTStub.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.ShaderVariantAnalyticsModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.UI.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", ]

